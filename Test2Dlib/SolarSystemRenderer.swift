//
//  SolarSystemRenderer.swift
//  Test2Dlib
//
//  Created by 王雷 on 4/6/25.
//

import Cocoa
import MetalKit
import CoreText
import LibTessSwift
import QuartzCore

// MARK: - 数据结构定义

struct Vertex {
    var position: SIMD2<Float>
}

struct Vertex3D {
    var position: SIMD3<Float>
    var normal: SIMD3<Float>
}

struct Transform {
    var modelMatrix: matrix_float4x4
    var viewMatrix: matrix_float4x4
    var projectionMatrix: matrix_float4x4
}

// MARK: - 路径信息结构体
struct PathInfo {
    var contourVertices: [SIMD2<Float>]
    var offset: SIMD2<Float>
    var scale: Float
    var tessellator: TessC
    
    // 静态方法：变换点坐标
    static func transformPoint(_ point: CGPoint, offset: SIMD2<Float>, scale: Float) -> SIMD2<Float> {
        return SIMD2<Float>(Float(point.x) * scale + offset.x, Float(point.y) * scale + offset.y)
    }
    
    // 静态方法：二次贝塞尔曲线细分
    static func subdivideBezier(start: SIMD2<Float>, control: SIMD2<Float>, end: SIMD2<Float>, subdivisions: Int = 10) -> [SIMD2<Float>] {
        var result: [SIMD2<Float>] = []
        for i in 1...subdivisions {
            let t = Float(i) / Float(subdivisions)
            let u = 1.0 - t
            let point = u * u * start + 2.0 * u * t * control + t * t * end
            result.append(point)
        }
        return result
    }
    
    // 静态方法：三次贝塞尔曲线细分
    static func subdivideCubicBezier(start: SIMD2<Float>, control1: SIMD2<Float>, 
                                    control2: SIMD2<Float>, end: SIMD2<Float>, subdivisions: Int = 10) -> [SIMD2<Float>] {
        var result: [SIMD2<Float>] = []
        for i in 1...subdivisions {
            let t = Float(i) / Float(subdivisions)
            let u = 1.0 - t
            let point = u * u * u * start + 
                        3.0 * u * u * t * control1 + 
                        3.0 * u * t * t * control2 + 
                        t * t * t * end
            result.append(point)
        }
        return result
    }
}

// MARK: - 太阳系模型参数
struct SolarSystemParams {
    static let sunRadius: Float = 0.08
    static let earthRadius: Float = 0.04
    static let orbitRadius: Float = 0.35
    static let earthOrbitSpeed: Float = 1.0 // 每秒弧度
    static let seasonChangeAngle: Float = Float.pi / 2 // 90度换一个季节
}

// MARK: - 主渲染器类
class SolarSystemRenderer: NSObject, MTKViewDelegate {
    
    // MARK: - Metal 基础组件
    private var device: MTLDevice!
    private var commandQueue: MTLCommandQueue!
    private var mtkView: MTKView!
    
    // MARK: - 渲染管线
    private var spherePipelineState: MTLRenderPipelineState?
    private var vectorPipelineState: MTLRenderPipelineState?
    private var depthStencilState: MTLDepthStencilState?
    
    // MARK: - 几何数据
    private var sunVertexBuffer: MTLBuffer?
    private var sunIndexBuffer: MTLBuffer?
    private var earthVertexBuffer: MTLBuffer?
    private var earthIndexBuffer: MTLBuffer?
    private var orbitVertexBuffer: MTLBuffer?
    private var vectorVertexBuffer: MTLBuffer?
    private var indexCount: Int = 0
    private var orbitVertexCount: Int = 0
    private var vectorVertexCount: Int = 0
    
    // MARK: - 变换矩阵
    private var transformBuffer: MTLBuffer?
    
    // MARK: - 动画状态
    private var animationTime: Double = 0.0
    private var displayLink: CVDisplayLink?
    private var lastFrameTime: CFTimeInterval = 0.0
    
    // MARK: - 向量渲染组件
    private var tessellator: TessC?
    private var currentSeason: Int = 0
    private let seasons = ["春", "夏", "秋", "冬"]
    
    // MARK: - 初始化方法
    init(device: MTLDevice, view: MTKView) {
        super.init()
        
        self.device = device
        self.mtkView = view
        self.commandQueue = device.makeCommandQueue()
        
        setupMTKView()
        setupRenderPipelines()
        setupDepthStencilState()
        setupGeometry()
        setupTransformBuffer()
        setupVectorRenderer()
        setupDisplayLink()
    }
    
    deinit {
        stopDisplayLink()
    }
    
    // MARK: - MTKView 配置
    private func setupMTKView() {
        mtkView.device = device
        mtkView.delegate = self
        mtkView.clearColor = MTLClearColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0) // 白色背景
        mtkView.depthStencilPixelFormat = .depth32Float
        mtkView.sampleCount = 4 // MSAA
        mtkView.preferredFramesPerSecond = 60
    }    
    // MARK: - 渲染管线设置
    private func setupRenderPipelines() {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }
        
        // 球体渲染管线
        setupSpherePipeline(library: library)
        
        // 向量渲染管线
        setupVectorPipeline(library: library)
    }
    
    private func setupSpherePipeline(library: MTLLibrary) {
        let vertexFunction = library.makeFunction(name: "sphere_vertex")
        let fragmentFunction = library.makeFunction(name: "sphere_fragment")
        
        // 配置顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 (attribute 0)
        vertexDescriptor.attributes[0].format = .float3
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        // 法向量属性 (attribute 1)
        vertexDescriptor.attributes[1].format = .float3
        vertexDescriptor.attributes[1].offset = MemoryLayout<SIMD3<Float>>.size
        vertexDescriptor.attributes[1].bufferIndex = 0
        
        // 顶点缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<SIMD3<Float>>.size * 2  // position + normal
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = mtkView.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = mtkView.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = mtkView.sampleCount
        
        // 启用混合
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha
        
        do {
            spherePipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建球体渲染管线失败: \(error)")
        }
    }
    
    private func setupVectorPipeline(library: MTLLibrary) {
        let vertexFunction = library.makeFunction(name: "vector_vertex")
        let fragmentFunction = library.makeFunction(name: "vector_fragment")
        
        // 配置顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 (attribute 0) - 2D顶点
        vertexDescriptor.attributes[0].format = .float2
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        // 顶点缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<SIMD2<Float>>.size
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = mtkView.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = mtkView.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = mtkView.sampleCount
        
        // 启用混合
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha
        
        do {
            vectorPipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建向量渲染管线失败: \(error)")
        }
    }    
    // MARK: - 深度模板状态
    private func setupDepthStencilState() {
        let depthStencilDescriptor = MTLDepthStencilDescriptor()
        depthStencilDescriptor.depthCompareFunction = .less
        depthStencilDescriptor.isDepthWriteEnabled = true
        depthStencilState = device.makeDepthStencilState(descriptor: depthStencilDescriptor)
    }
    
    // MARK: - 几何数据设置
    private func setupGeometry() {
        createSphereGeometry()
        createOrbitGeometry()
    }
    
    private func createSphereGeometry() {
        let (vertices, indices) = generateSphere(radius: 1.0, segments: 32)
        
        // 太阳顶点缓冲
        sunVertexBuffer = device.makeBuffer(bytes: vertices, 
                                          length: vertices.count * MemoryLayout<Vertex3D>.stride,
                                          options: [])
        
        // 地球顶点缓冲（使用相同的几何数据）
        earthVertexBuffer = device.makeBuffer(bytes: vertices,
                                            length: vertices.count * MemoryLayout<Vertex3D>.stride,
                                            options: [])
        
        // 索引缓冲
        sunIndexBuffer = device.makeBuffer(bytes: indices,
                                         length: indices.count * MemoryLayout<UInt16>.stride,
                                         options: [])
        earthIndexBuffer = device.makeBuffer(bytes: indices,
                                           length: indices.count * MemoryLayout<UInt16>.stride,
                                           options: [])
        
        indexCount = indices.count
    }
    
    private func generateSphere(radius: Float, segments: Int) -> ([Vertex3D], [UInt16]) {
        var vertices: [Vertex3D] = []
        var indices: [UInt16] = []
        
        // 生成球体顶点
        for i in 0...segments {
            let theta = Float(i) * Float.pi / Float(segments)
            for j in 0...segments {
                let phi = Float(j) * 2.0 * Float.pi / Float(segments)
                
                let x = radius * sin(theta) * cos(phi)
                let y = radius * cos(theta)
                let z = radius * sin(theta) * sin(phi)
                
                let position = SIMD3<Float>(x, y, z)
                let normal = normalize(position)
                
                vertices.append(Vertex3D(position: position, normal: normal))
            }
        }
        
        // 生成球体索引
        for i in 0..<segments {
            for j in 0..<segments {
                let first = UInt16(i * (segments + 1) + j)
                let second = UInt16(first + UInt16(segments + 1))
                
                indices.append(contentsOf: [
                    first, second, first + 1,
                    second, second + 1, first + 1
                ])
            }
        }
        
        return (vertices, indices)
    }
    
    private func createOrbitGeometry() {
        var orbitVertices: [Vertex] = []
        let segments = 360
        
        for i in 0...segments {
            let angle = Float(i) * 2.0 * Float.pi / Float(segments)
            let x = SolarSystemParams.orbitRadius * cos(angle)
            let y = SolarSystemParams.orbitRadius * sin(angle)
            orbitVertices.append(Vertex(position: SIMD2<Float>(x, y)))
        }
        
        orbitVertexBuffer = device.makeBuffer(bytes: orbitVertices,
                                            length: orbitVertices.count * MemoryLayout<Vertex>.stride,
                                            options: [])
        orbitVertexCount = orbitVertices.count
    }
    
    // MARK: - 变换矩阵设置
    private func setupTransformBuffer() {
        transformBuffer = device.makeBuffer(length: MemoryLayout<Transform>.stride, options: [])
    }
    
    private func updateTransforms() {
        let aspectRatio = Float(mtkView.drawableSize.width / mtkView.drawableSize.height)
        
        // 投影矩阵（正交投影）
        let projectionMatrix = orthographicProjection(left: -aspectRatio, right: aspectRatio,
                                                    bottom: -1.0, top: 1.0,
                                                    near: -1.0, far: 1.0)
        
        // 视图矩阵（单位矩阵）
        let viewMatrix = matrix_identity_float4x4
        
        // 模型矩阵（单位矩阵）
        let modelMatrix = matrix_identity_float4x4
        
        let transform = Transform(modelMatrix: modelMatrix,
                                viewMatrix: viewMatrix,
                                projectionMatrix: projectionMatrix)
        
        let transformPointer = transformBuffer?.contents().bindMemory(to: Transform.self, capacity: 1)
        transformPointer?.pointee = transform
    }
    
    // MARK: - 向量渲染设置
    private func setupVectorRenderer() {
        tessellator = TessC()
        guard tessellator != nil else {
            fatalError("无法初始化 LibTessSwift")
        }
    }    
    private func generateSeasonText() -> [Vertex] {
        var vertices: [Vertex] = []
        
        // 计算当前季节
        let seasonIndex = Int(animationTime / Double(SolarSystemParams.seasonChangeAngle)) % 4
        currentSeason = seasonIndex
        
        let seasonText = seasons[seasonIndex]
        
        // 使用 CoreText 生成文字路径
        if let textVertices = generateTextVertices(text: seasonText, size: 0.1) {
            vertices.append(contentsOf: textVertices)
        }
        
        return vertices
    }
    
    private func generateTextVertices(text: String, size: Float) -> [Vertex]? {
        guard let tessellator = tessellator else { return nil }
        
        // 创建字体和属性字符串
        let font = NSFont.systemFont(ofSize: CGFloat(size * 100))
        let attributes: [NSAttributedString.Key: Any] = [.font: font]
        let attributedString = NSAttributedString(string: text, attributes: attributes)
        
        // 创建 CTLine
        let line = CTLineCreateWithAttributedString(attributedString)
        let runs = CTLineGetGlyphRuns(line)
        let runCount = CFArrayGetCount(runs)
        
        var allVertices: [Vertex] = []
        
        for runIndex in 0..<runCount {
            let run = unsafeBitCast(CFArrayGetValueAtIndex(runs, runIndex), to: CTRun.self)
            let glyphCount = CTRunGetGlyphCount(run)
            
            // 获取字形和位置
            var glyphs = Array<CGGlyph>(repeating: 0, count: glyphCount)
            var positions = Array<CGPoint>(repeating: .zero, count: glyphCount)
            
            CTRunGetGlyphs(run, CFRangeMake(0, 0), &glyphs)
            CTRunGetPositions(run, CFRangeMake(0, 0), &positions)
            
            // 获取字体
            let runAttributes = CTRunGetAttributes(run)
            let runFont = unsafeBitCast(CFDictionaryGetValue(runAttributes, 
                                                           Unmanaged.passUnretained(kCTFontAttributeName).toOpaque()),
                                      to: CTFont.self)
            
            for glyphIndex in 0..<glyphCount {
                let glyph = glyphs[glyphIndex]
                let position = positions[glyphIndex]
                
                // 创建字形路径
                if let glyphPath = CTFontCreatePathForGlyph(runFont, glyph, nil) {
                    let vertices = tessellateGlyphPath(path: glyphPath, 
                                                     offset: SIMD2<Float>(Float(position.x), Float(position.y)),
                                                     scale: size)
                    allVertices.append(contentsOf: vertices)
                }
            }
        }
        
        return allVertices
    }    
    private func tessellateGlyphPath(path: CGPath, offset: SIMD2<Float>, scale: Float) -> [Vertex] {
        guard let tessellator = tessellator else { return [] }
        
        var vertices: [Vertex] = []
        
        // 添加路径轮廓
        addPathToTessellator(path: path, tessellator: tessellator, offset: offset, scale: scale)
        
        // 执行三角剖分
        do {
            let result = try tessellator.tessellate(windingRule: .evenOdd, elementType: .polygons, polySize: 3)
            let tessVertices = result.vertices
            let tessIndices = result.indices
            
            // 转换为我们的顶点格式
            for i in stride(from: 0, to: tessIndices.count, by: 3) {
                for j in 0..<3 {
                    let index = tessIndices[i + j]
                    if index < tessVertices.count {
                        let vertex = tessVertices[index]
                        vertices.append(Vertex(position: SIMD2<Float>(vertex.x, vertex.y)))
                    }
                }
            }
        } catch {
            // 三角剖分失败，返回空数组
        }
        
        return vertices
    }
    
    private func addPathToTessellator(path: CGPath, tessellator: TessC, 
                                    offset: SIMD2<Float>, scale: Float) {
        var contourVertices: [SIMD2<Float>] = []
        
        // 手动遍历路径元素，避免使用需要捕获上下文的闭包
        let pathInfo = UnsafeMutablePointer<PathInfo>.allocate(capacity: 1)
        pathInfo.pointee = PathInfo(contourVertices: contourVertices, offset: offset, scale: scale, tessellator: tessellator)
        
        path.apply(info: pathInfo) { (info, element) in
            guard let pathInfo = info?.assumingMemoryBound(to: PathInfo.self) else { return }
            
            let points = element.pointee.points
            
            switch element.pointee.type {
            case .moveToPoint:
                if !pathInfo.pointee.contourVertices.isEmpty {
                    // 添加之前的轮廓
                    let vector3Vertices = pathInfo.pointee.contourVertices.map { SIMD3<Float>($0.x, $0.y, 0.0) }
                    pathInfo.pointee.tessellator.addContour(vector3Vertices)
                    pathInfo.pointee.contourVertices.removeAll()
                }
                let point = PathInfo.transformPoint(points[0], offset: pathInfo.pointee.offset, scale: pathInfo.pointee.scale)
                pathInfo.pointee.contourVertices.append(point)
                
            case .addLineToPoint:
                let point = PathInfo.transformPoint(points[0], offset: pathInfo.pointee.offset, scale: pathInfo.pointee.scale)
                pathInfo.pointee.contourVertices.append(point)
                
            case .addQuadCurveToPoint:
                // 将二次贝塞尔曲线分解为线段
                let currentPoint = pathInfo.pointee.contourVertices.last ?? SIMD2<Float>(0, 0)
                let controlPoint = PathInfo.transformPoint(points[0], offset: pathInfo.pointee.offset, scale: pathInfo.pointee.scale)
                let endPoint = PathInfo.transformPoint(points[1], offset: pathInfo.pointee.offset, scale: pathInfo.pointee.scale)
                
                let segments = PathInfo.subdivideBezier(start: currentPoint, control: controlPoint, end: endPoint)
                pathInfo.pointee.contourVertices.append(contentsOf: segments)
                
            case .addCurveToPoint:
                // 将三次贝塞尔曲线分解为线段
                let currentPoint = pathInfo.pointee.contourVertices.last ?? SIMD2<Float>(0, 0)
                let control1 = PathInfo.transformPoint(points[0], offset: pathInfo.pointee.offset, scale: pathInfo.pointee.scale)
                let control2 = PathInfo.transformPoint(points[1], offset: pathInfo.pointee.offset, scale: pathInfo.pointee.scale)
                let endPoint = PathInfo.transformPoint(points[2], offset: pathInfo.pointee.offset, scale: pathInfo.pointee.scale)
                
                let segments = PathInfo.subdivideCubicBezier(start: currentPoint, 
                                                  control1: control1, 
                                                  control2: control2, 
                                                  end: endPoint)
                pathInfo.pointee.contourVertices.append(contentsOf: segments)
                
            case .closeSubpath:
                if let first = pathInfo.pointee.contourVertices.first {
                    pathInfo.pointee.contourVertices.append(first)
                }
            @unknown default:
                break
            }
        }
        
        // 添加最后的轮廓
        if !pathInfo.pointee.contourVertices.isEmpty {
            let vector3Vertices = pathInfo.pointee.contourVertices.map { SIMD3<Float>($0.x, $0.y, 0.0) }
            tessellator.addContour(vector3Vertices)
        }
        
        pathInfo.deallocate()
    }
    
    // MARK: - CVDisplayLink 动画控制
    private func setupDisplayLink() {
        var displayLink: CVDisplayLink?
        CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
        
        guard let displayLink = displayLink else {
            fatalError("无法创建 CVDisplayLink")
        }
        
        let callback: CVDisplayLinkOutputCallback = { (displayLink, now, outputTime, flagsIn, flagsOut, displayLinkContext) -> CVReturn in
            let renderer = Unmanaged<SolarSystemRenderer>.fromOpaque(displayLinkContext!).takeUnretainedValue()
            renderer.updateAnimation(time: CFTimeInterval(outputTime.pointee.videoTime) / CFTimeInterval(outputTime.pointee.videoTimeScale))
            return kCVReturnSuccess
        }
        
        CVDisplayLinkSetOutputCallback(displayLink, callback, UnsafeMutableRawPointer(Unmanaged.passUnretained(self).toOpaque()))
        
        self.displayLink = displayLink
        lastFrameTime = CACurrentMediaTime()
        
        CVDisplayLinkStart(displayLink)
    }    
    private func stopDisplayLink() {
        if let displayLink = displayLink {
            CVDisplayLinkStop(displayLink)
        }
    }
    
    private func updateAnimation(time: CFTimeInterval) {
        let deltaTime = time - lastFrameTime
        lastFrameTime = time
        
        animationTime += deltaTime * Double(SolarSystemParams.earthOrbitSpeed)
        
        DispatchQueue.main.async {
            self.mtkView.needsDisplay = true
        }
    }
    
    // MARK: - 数学辅助函数
    private func orthographicProjection(left: Float, right: Float, bottom: Float, 
                                      top: Float, near: Float, far: Float) -> matrix_float4x4 {
        let x = 2.0 / (right - left)
        let y = 2.0 / (top - bottom)
        let z = -2.0 / (far - near)
        let tx = -(right + left) / (right - left)
        let ty = -(top + bottom) / (top - bottom)
        let tz = -(far + near) / (far - near)
        
        return matrix_float4x4(columns: (
            SIMD4<Float>(x, 0, 0, 0),
            SIMD4<Float>(0, y, 0, 0),
            SIMD4<Float>(0, 0, z, 0),
            SIMD4<Float>(tx, ty, tz, 1)
        ))
    }
    
    private func translationMatrix(_ translation: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(1, 0, 0, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(translation.x, translation.y, translation.z, 1)
        ))
    }
    
    private func scaleMatrix(_ scale: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(scale.x, 0, 0, 0),
            SIMD4<Float>(0, scale.y, 0, 0),
            SIMD4<Float>(0, 0, scale.z, 0),
            SIMD4<Float>(0, 0, 0, 1)
        ))
    }
}

// MARK: - MTKViewDelegate 实现
extension SolarSystemRenderer {
    
    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
        // 当视图尺寸改变时更新投影矩阵
        updateProjectionMatrix(size: size)
    }
    
    func updateProjectionMatrix(size: CGSize) {
        updateTransforms()
    }
    
    func draw(in view: MTKView) {
        guard let drawable = view.currentDrawable,
              let renderPassDescriptor = view.currentRenderPassDescriptor,
              let commandBuffer = commandQueue.makeCommandBuffer(),
              let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else {
            return
        }
        
        // 更新变换矩阵
        updateTransforms()
        
        // 设置深度模板状态
        renderEncoder.setDepthStencilState(depthStencilState)
        
        // 渲染轨道路径
        renderOrbitPath(encoder: renderEncoder)
        
        // 渲染太阳
        renderSun(encoder: renderEncoder)
        
        // 渲染地球
        renderEarth(encoder: renderEncoder)
        
        // 渲染季节文字
        renderSeasonText(encoder: renderEncoder)
        
        renderEncoder.endEncoding()
        commandBuffer.present(drawable)
        commandBuffer.commit()
    }    
    private func renderOrbitPath(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = vectorPipelineState,
              let orbitBuffer = orbitVertexBuffer else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(orbitBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .lineStrip, vertexStart: 0, vertexCount: orbitVertexCount)
    }
    
    private func renderSun(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = spherePipelineState,
              let vertexBuffer = sunVertexBuffer,
              let indexBuffer = sunIndexBuffer else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 太阳的变换矩阵（在原点，缩放为太阳大小）
        var sunScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.sunRadius, 
                                              SolarSystemParams.sunRadius, 
                                              SolarSystemParams.sunRadius))
        encoder.setVertexBytes(&sunScale, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle, 
                                    indexCount: indexCount, 
                                    indexType: .uint16, 
                                    indexBuffer: indexBuffer, 
                                    indexBufferOffset: 0)
    }    
    private func renderEarth(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = spherePipelineState,
              let vertexBuffer = earthVertexBuffer,
              let indexBuffer = earthIndexBuffer else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 计算地球位置
        let angle = Float(animationTime)
        let earthX = SolarSystemParams.orbitRadius * cos(angle)
        let earthY = SolarSystemParams.orbitRadius * sin(angle)
        
        // 地球的变换矩阵（移动到轨道位置，缩放为地球大小）
        let earthTranslation = translationMatrix(SIMD3<Float>(earthX, earthY, 0.0))
        let earthScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.earthRadius, 
                                                SolarSystemParams.earthRadius, 
                                                SolarSystemParams.earthRadius))
        var earthTransform = earthTranslation * earthScale
        
        encoder.setVertexBytes(&earthTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle, 
                                    indexCount: indexCount, 
                                    indexType: .uint16, 
                                    indexBuffer: indexBuffer, 
                                    indexBufferOffset: 0)
    }
    
    private func renderSeasonText(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = vectorPipelineState else { return }
        
        // 生成季节文字的三角形
        let textVertices = generateSeasonText()
        guard !textVertices.isEmpty else { return }
        
        // 创建临时顶点缓冲
        guard let textBuffer = device.makeBuffer(bytes: textVertices,
                                               length: textVertices.count * MemoryLayout<Vertex>.stride,
                                               options: []) else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(textBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .triangle, vertexStart: 0, vertexCount: textVertices.count)
    }
}